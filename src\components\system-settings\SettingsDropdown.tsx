"use client";
import React from "react";
import { IoChevronDown } from "react-icons/io5";

interface SettingsDropdownProps {
  id: string;
  value: string;
  options: string[];
  isOpen: boolean;
  onToggle: () => void;
  onSelect: (value: string) => void;
  error?: string;
  placeholder?: string;
}

const SettingsDropdown: React.FC<SettingsDropdownProps> = ({
  id,
  value,
  options,
  isOpen,
  onToggle,
  onSelect,
  error,
  placeholder = "Select option",
}) => {
  return (
    <div className="relative">
      <div
        className={`w-fit gap-3 text-white text-sm py-4 px-4 cursor-pointer flex items-center justify-between focus:outline-none border ${
          error
            ? "border-red-500 focus:ring-red-500"
            : "border-[#2E2E2E]"
        } transition-colors`}
        onClick={onToggle}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onToggle();
          }
        }}
      >
        <span className={value ? "text-white" : "text-[#8A8A8A]"}>
          {value || placeholder}
        </span>
        <IoChevronDown
          size={16}
          className={`transition-transform ${isOpen ? "rotate-180" : ""}`}
        />
      </div>
      
      {isOpen && (
        <div className="absolute top-full left-0 w-full bg-[#2E2E2E] border border-[#3D3D3D] z-10 max-h-48 overflow-y-auto" role="listbox" aria-label="Options">
          {options
            .filter((option) => option !== value)
            .map((option) => (
              <div
                key={option}
                className="px-4 py-3 cursor-pointer text-sm text-[#E4E7EC] hover:bg-[#3D3D3D] transition-colors"
                onClick={() => onSelect(option)}
                role="option"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    onSelect(option);
                  }
                }}
              >
                {option}
              </div>
            ))}
        </div>
      )}
      
      {error && (
        <p className="text-red-500 text-xs mt-1 error-message">{error}</p>
      )}
    </div>
  );
};

export default SettingsDropdown;
