import { useState, useCallback, useEffect } from "react";
import {
  SystemSettingsFormData,
  SystemSettingsErrors,
  GeneralPreferences,
  AlertNotificationSettings,
  FaceDirectorySettings,
  SecurityPreferences,
} from "@/types/systemSettingsTypes";
import { showDetailedErrorToast } from "@/components/ErrorToast";
import toast from "react-hot-toast";

// Mock initial data - replace with API call
const getInitialSettings = (): SystemSettingsFormData => ({
  generalPreferences: {
    timezone: "(UTC+1) West Africa Time",
    dateFormat: "DD/MM/YYYY",
    timeFormat: "24-Hour Format",
    language: "English",
  },
  alertNotificationSettings: {
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    securityAlerts: true,
    systemUpdates: true,
    maintenanceAlerts: false,
  },
  faceDirectorySettings: {
    enableFaceRecognition: true,
    confidenceThreshold: 85,
    autoEnrollment: false,
    retentionPeriod: 30,
  },
  securityPreferences: {
    sessionTimeout: 30,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
    },
    twoFactorAuth: false,
    loginAttempts: 3,
  },
});

export const useSystemSettings = () => {
  const [formData, setFormData] = useState<SystemSettingsFormData>(getInitialSettings());
  const [originalData, setOriginalData] = useState<SystemSettingsFormData>(getInitialSettings());
  const [errors, setErrors] = useState<SystemSettingsErrors>({
    generalPreferences: {},
    alertNotificationSettings: {},
    faceDirectorySettings: {},
    securityPreferences: {},
  });
  const [loading, setLoading] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        // TODO: Replace with actual API call
        // const settings = await systemSettingsService.getSettings();
        const settings = getInitialSettings();
        setFormData(settings);
        setOriginalData(settings);
      } catch (error) {
        showDetailedErrorToast("Failed to load system settings", error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Check if there are unsaved changes
  const hasChanges = useCallback(() => {
    return JSON.stringify(formData) !== JSON.stringify(originalData);
  }, [formData, originalData]);

  // Handle general preferences changes
  const handleGeneralPreferencesChange = useCallback((field: keyof GeneralPreferences, value: string) => {
    setFormData(prev => ({
      ...prev,
      generalPreferences: {
        ...prev.generalPreferences,
        [field]: value,
      },
    }));

    // Clear error for this field
    setErrors(prev => ({
      ...prev,
      generalPreferences: {
        ...prev.generalPreferences,
        [field]: undefined,
      },
    }));
  }, []);

  // Handle alert notification changes
  const handleAlertNotificationChange = useCallback((field: keyof AlertNotificationSettings, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      alertNotificationSettings: {
        ...prev.alertNotificationSettings,
        [field]: value,
      },
    }));
  }, []);

  // Handle face directory changes
  const handleFaceDirectoryChange = useCallback((field: keyof FaceDirectorySettings, value: boolean | number) => {
    setFormData(prev => ({
      ...prev,
      faceDirectorySettings: {
        ...prev.faceDirectorySettings,
        [field]: value,
      },
    }));
  }, []);

  // Handle security preferences changes
  const handleSecurityPreferencesChange = useCallback((field: keyof SecurityPreferences | string, value: any) => {
    setFormData(prev => {
      if (field.includes('.')) {
        // Handle nested fields like passwordPolicy.minLength
        const [parentField, childField] = field.split('.');
        return {
          ...prev,
          securityPreferences: {
            ...prev.securityPreferences,
            [parentField]: {
              ...(prev.securityPreferences as any)[parentField],
              [childField]: value,
            },
          },
        };
      } else {
        return {
          ...prev,
          securityPreferences: {
            ...prev.securityPreferences,
            [field]: value,
          },
        };
      }
    });
  }, []);

  // Save settings
  const handleSave = useCallback(async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // await systemSettingsService.updateSettings(formData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setOriginalData(formData);
      toast.success("Settings saved successfully");
    } catch (error) {
      showDetailedErrorToast("Failed to save settings", error);
    } finally {
      setLoading(false);
    }
  }, [formData]);

  // Reset to original values
  const handleReset = useCallback(() => {
    setFormData(originalData);
    setErrors({
      generalPreferences: {},
      alertNotificationSettings: {},
      faceDirectorySettings: {},
      securityPreferences: {},
    });
  }, [originalData]);

  return {
    formData,
    errors,
    loading,
    hasChanges: hasChanges(),
    handleGeneralPreferencesChange,
    handleAlertNotificationChange,
    handleFaceDirectoryChange,
    handleSecurityPreferencesChange,
    handleSave,
    handleReset,
  };
};
