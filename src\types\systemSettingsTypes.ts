export interface GeneralPreferences {
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  language: string;
}

export interface AlertNotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  securityAlerts: boolean;
  systemUpdates: boolean;
  maintenanceAlerts: boolean;
}

export interface FaceDirectorySettings {
  enableFaceRecognition: boolean;
  confidenceThreshold: number;
  autoEnrollment: boolean;
  retentionPeriod: number;
}

export interface SecurityPreferences {
  sessionTimeout: number;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  twoFactorAuth: boolean;
  loginAttempts: number;
}

export interface SystemSettings {
  generalPreferences: GeneralPreferences;
  alertNotificationSettings: AlertNotificationSettings;
  faceDirectorySettings: FaceDirectorySettings;
  securityPreferences: SecurityPreferences;
}

export interface SystemSettingsFormData {
  generalPreferences: GeneralPreferences;
  alertNotificationSettings: AlertNotificationSettings;
  faceDirectorySettings: FaceDirectorySettings;
  securityPreferences: SecurityPreferences;
}

export interface SystemSettingsErrors {
  generalPreferences: Partial<GeneralPreferences>;
  alertNotificationSettings: Partial<AlertNotificationSettings>;
  faceDirectorySettings: Partial<FaceDirectorySettings>;
  securityPreferences: Partial<SecurityPreferences>;
}

export type SettingsCategory = 
  | "general" 
  | "notifications" 
  | "faceDirectory" 
  | "security";

export interface SettingsSidebarItem {
  id: SettingsCategory;
  label: string;
  icon?: React.ComponentType<{ size?: number; className?: string }>;
}
