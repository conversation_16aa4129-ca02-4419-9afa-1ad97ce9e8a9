"use client";
import React from "react";

interface SettingsActionButtonsProps {
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

const SettingsActionButtons: React.FC<SettingsActionButtonsProps> = ({
  onSave,
  onReset,
  loading,
  hasChanges,
}) => {
  return (
    <div className="flex items-center justify-end space-x-4 pt-6 border-t border-[#3D3D3D]">
      <button
        type="button"
        onClick={onReset}
        disabled={!hasChanges || loading}
        className={`px-6 py-2 text-sm border transition-colors ${
          hasChanges && !loading
            ? "border-[#3D3D3D] text-[#E4E7EC] hover:bg-[#2A2A2A]"
            : "border-[#2A2A2A] text-[#6A6A6A] cursor-not-allowed"
        }`}
      >
        Reset
      </button>
      
      <button
        type="button"
        onClick={onSave}
        disabled={!hasChanges || loading}
        className={`px-6 py-2 text-sm transition-colors ${
          hasChanges && !loading
            ? "bg-[#A3A3A3] text-[#242424] hover:bg-[#B3B3B3]"
            : "bg-[#3D3D3D] text-[#6A6A6A] cursor-not-allowed"
        }`}
      >
        {loading ? "Saving..." : "Save Changes"}
      </button>
    </div>
  );
};

export default SettingsActionButtons;
