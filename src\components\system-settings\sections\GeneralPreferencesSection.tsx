"use client";
import React, { useState } from "react";
import { GeneralPreferences } from "@/types/systemSettingsTypes";
import SettingsDropdown from "../SettingsDropdown";


interface GeneralPreferencesSectionProps {
  data: GeneralPreferences;
  errors: Partial<GeneralPreferences>;
  onChange: (field: keyof GeneralPreferences, value: string) => void;
}

const TIMEZONE_OPTIONS = [
  "(UTC+1) West Africa Time",
  "(UTC+0) Greenwich Mean Time",
  "(UTC-5) Eastern Standard Time",
  "(UTC-8) Pacific Standard Time",
  "(UTC+1) Central European Time",
  "(UTC+8) China Standard Time",
];

const DATE_FORMAT_OPTIONS = [
  "DD/MM/YYYY",
  "MM/DD/YYYY",
];

const TIME_FORMAT_OPTIONS = [
  "24-Hour Format",
  "12-Hour Format",
];



const GeneralPreferencesSection: React.FC<GeneralPreferencesSectionProps> = ({
  data,
  errors,
  onChange,
}) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const handleDropdownToggle = (dropdownId: string) => {
    setOpenDropdown(openDropdown === dropdownId ? null : dropdownId);
  };

  const handleDropdownSelect = (field: keyof GeneralPreferences, value: string) => {
    onChange(field, value);
    setOpenDropdown(null);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-[#3D3D3D] pb-4">
        <h2 className="text-xl text-white font-medium">General Preferences</h2>
        <p className="text-[#A3A3A3] text-sm mt-1">
          Adjust how time, date, and language appear across your platform.
        </p>
      </div>

      {/* Settings Form */}
      <div className="space-y-8">
        {/* Timezone */}
        <div className="flex items-center justify-between">
          <div className="flex-1 max-w-md">
            <label className="block text-lg text-white font-medium mb-2">Timezone</label>
            <p className="text-sm text-[#A3A3A3] max-w-xs">
              Set your system&apos;s local timezone to align logs and activities across all connected devices.
            </p>
          </div>
          <div className="flex-shrink-0">
            <SettingsDropdown
              id="timezone"
              value={data.timezone}
              options={TIMEZONE_OPTIONS}
              isOpen={openDropdown === "timezone"}
              onToggle={() => handleDropdownToggle("timezone")}
              onSelect={(value) => handleDropdownSelect("timezone", value)}
              error={errors.timezone}
            />
          </div>
        </div>

        {/* Date Format */}
        <div className="flex items-center justify-between">
          <div className="flex-1 max-w-md">
            <label className="block text-lg text-white font-medium mb-2">Date Format</label>
            <p className="text-sm text-[#A3A3A3] max-w-xs">
              Choose how you&apos;d like dates to appear on reports and logs across the platform.
            </p>
          </div>
          <div className="flex-shrink-0">
            <SettingsDropdown
              id="dateFormat"
              value={data.dateFormat}
              options={DATE_FORMAT_OPTIONS}
              isOpen={openDropdown === "dateFormat"}
              onToggle={() => handleDropdownToggle("dateFormat")}
              onSelect={(value) => handleDropdownSelect("dateFormat", value)}
              error={errors.dateFormat}
            />
          </div>
        </div>

        {/* Time Format */}
        <div className="flex items-center justify-between">
          <div className="flex-1 max-w-md">
            <label className="block text-lg text-white font-medium mb-2">Time Format</label>
            <p className="text-sm text-[#A3A3A3] max-w-xs">
              Switch between 24-hour and 12-hour time display formats based on preference.
            </p>
          </div>
          <div className="flex-shrink-0">
            <SettingsDropdown
              id="timeFormat"
              value={data.timeFormat}
              options={TIME_FORMAT_OPTIONS}
              isOpen={openDropdown === "timeFormat"}
              onToggle={() => handleDropdownToggle("timeFormat")}
              onSelect={(value) => handleDropdownSelect("timeFormat", value)}
              error={errors.timeFormat}
            />
          </div>
        </div>

        {/* Language */}
        <div className="flex items-center justify-between">
          <div className="flex-1 max-w-md">
            <label className="block text-lg text-white font-medium mb-2">Language</label>
            <p className="text-sm text-[#A3A3A3] max-w-xs">
              The default language for all system labels and interface. More languages coming soon.
            </p>
          </div>
          <div className="flex-shrink-0">
            <div
              className="text-white py-4 px-10 w-fit"
              style={{
                background: "linear-gradient(145.42deg, rgba(31, 31, 31, 0.5) 32.16%, rgba(112, 112, 112, 0.5) 237.44%)"
              }}
            >
              <span>English</span>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default GeneralPreferencesSection;
