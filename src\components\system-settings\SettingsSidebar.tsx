"use client";
import React from "react";
import { SettingsCategory, SettingsSidebarItem } from "@/types/systemSettingsTypes";

interface SettingsSidebarProps {
  activeCategory: SettingsCategory;
  onCategoryChange: (category: SettingsCategory) => void;
}

const SIDEBAR_ITEMS: SettingsSidebarItem[] = [
  {
    id: "general",
    label: "General Preference",
  },
  {
    id: "notifications",
    label: "Alert & Notification Settings",
  },
  {
    id: "faceDirectory",
    label: "Face Directory Management",
  },
  {
    id: "security",
    label: "Security Preferences",
  },
];

const SettingsSidebar: React.FC<SettingsSidebarProps> = ({
  activeCategory,
  onCategoryChange,
}) => {
  return (
    <div className="border-r border-[#3D3D3D] h-full">
      <div className="py-6 pr-8">
        <nav className="space-y-2">
          {SIDEBAR_ITEMS.map((item) => {
            const isActive = activeCategory === item.id;

            return (
              <button
                type="button"
                key={item.id}
                onClick={() => onCategoryChange(item.id)}
                className={`w-full cursor-pointer text-left px-5 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-[#1F1F1F] text-white"
                    : "text-[#A3A3A3]"
                }`}
              >
                <span className="text-sm">{item.label}</span>
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default SettingsSidebar;
