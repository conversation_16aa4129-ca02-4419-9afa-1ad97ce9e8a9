"use client";
import React from "react";
import { FaceDirectorySettings } from "@/types/systemSettingsTypes";
import SettingsToggle from "../SettingsToggle";
import SettingsSlider from "../SettingsSlider";
import SettingsActionButtons from "../SettingsActionButtons";

interface FaceDirectorySectionProps {
  data: FaceDirectorySettings;
  errors: Partial<FaceDirectorySettings>;
  onChange: (field: keyof FaceDirectorySettings, value: boolean | number) => void;
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

const FaceDirectorySection: React.FC<FaceDirectorySectionProps> = ({
  data,
  errors,
  onChange,
  onSave,
  onReset,
  loading,
  hasChanges,
}) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-[#3D3D3D] pb-4">
        <h2 className="text-xl text-white font-medium">Face Directory Management</h2>
        <p className="text-[#A3A3A3] text-sm mt-1">
          Configure face recognition settings and data retention policies.
        </p>
      </div>

      {/* Settings Form */}
      <div className="space-y-6">
        {/* Face Recognition */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Face Recognition</h3>
          <div className="space-y-4">
            <SettingsToggle
              id="enableFaceRecognition"
              label="Enable Face Recognition"
              description="Allow the system to detect and recognize faces in camera feeds"
              checked={data.enableFaceRecognition}
              onChange={(checked) => onChange("enableFaceRecognition", checked)}
              error={errors.enableFaceRecognition as string}
            />
            
            <SettingsSlider
              id="confidenceThreshold"
              label="Confidence Threshold"
              description="Minimum confidence level required for face recognition matches (higher values = more accurate but fewer matches)"
              value={data.confidenceThreshold}
              min={50}
              max={99}
              step={1}
              unit="%"
              onChange={(value) => onChange("confidenceThreshold", value)}
              error={errors.confidenceThreshold as string}
              disabled={!data.enableFaceRecognition}
            />
          </div>
        </div>

        {/* Enrollment Settings */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Enrollment Settings</h3>
          <div className="space-y-4">
            <SettingsToggle
              id="autoEnrollment"
              label="Auto Enrollment"
              description="Automatically add new faces to the directory when detected"
              checked={data.autoEnrollment}
              onChange={(checked) => onChange("autoEnrollment", checked)}
              error={errors.autoEnrollment as string}
              disabled={!data.enableFaceRecognition}
            />
          </div>
        </div>

        {/* Data Retention */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Data Retention</h3>
          <div className="space-y-4">
            <SettingsSlider
              id="retentionPeriod"
              label="Retention Period"
              description="How long to keep face recognition data before automatic deletion"
              value={data.retentionPeriod}
              min={7}
              max={365}
              step={1}
              unit=" days"
              onChange={(value) => onChange("retentionPeriod", value)}
              error={errors.retentionPeriod as string}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <SettingsActionButtons
        onSave={onSave}
        onReset={onReset}
        loading={loading}
        hasChanges={hasChanges}
      />
    </div>
  );
};

export default FaceDirectorySection;
