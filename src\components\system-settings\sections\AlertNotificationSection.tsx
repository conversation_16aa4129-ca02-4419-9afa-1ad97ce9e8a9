"use client";
import React from "react";
import { AlertNotificationSettings } from "@/types/systemSettingsTypes";
import SettingsToggle from "../SettingsToggle";
import SettingsActionButtons from "../SettingsActionButtons";

interface AlertNotificationSectionProps {
  data: AlertNotificationSettings;
  errors: Partial<AlertNotificationSettings>;
  onChange: (field: keyof AlertNotificationSettings, value: boolean) => void;
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

const AlertNotificationSection: React.FC<AlertNotificationSectionProps> = ({
  data,
  errors,
  onChange,
  onSave,
  onReset,
  loading,
  hasChanges,
}) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-[#3D3D3D] pb-4">
        <h2 className="text-xl text-white font-medium">Alert & Notification Settings</h2>
        <p className="text-[#A3A3A3] text-sm mt-1">
          Configure how and when you receive notifications about system events.
        </p>
      </div>

      {/* Settings Form */}
      <div className="space-y-6">
        {/* Communication Preferences */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Communication Preferences</h3>
          <div className="space-y-4">
            <SettingsToggle
              id="emailNotifications"
              label="Email Notifications"
              description="Receive notifications via email"
              checked={data.emailNotifications}
              onChange={(checked) => onChange("emailNotifications", checked)}
              error={errors.emailNotifications as string}
            />
            
            <SettingsToggle
              id="pushNotifications"
              label="Push Notifications"
              description="Receive browser push notifications"
              checked={data.pushNotifications}
              onChange={(checked) => onChange("pushNotifications", checked)}
              error={errors.pushNotifications as string}
            />
            
            <SettingsToggle
              id="smsNotifications"
              label="SMS Notifications"
              description="Receive notifications via SMS (additional charges may apply)"
              checked={data.smsNotifications}
              onChange={(checked) => onChange("smsNotifications", checked)}
              error={errors.smsNotifications as string}
            />
          </div>
        </div>

        {/* Alert Types */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Alert Types</h3>
          <div className="space-y-4">
            <SettingsToggle
              id="securityAlerts"
              label="Security Alerts"
              description="Notifications for security events and breaches"
              checked={data.securityAlerts}
              onChange={(checked) => onChange("securityAlerts", checked)}
              error={errors.securityAlerts as string}
            />
            
            <SettingsToggle
              id="systemUpdates"
              label="System Updates"
              description="Notifications about system updates and maintenance"
              checked={data.systemUpdates}
              onChange={(checked) => onChange("systemUpdates", checked)}
              error={errors.systemUpdates as string}
            />
            
            <SettingsToggle
              id="maintenanceAlerts"
              label="Maintenance Alerts"
              description="Notifications about scheduled maintenance windows"
              checked={data.maintenanceAlerts}
              onChange={(checked) => onChange("maintenanceAlerts", checked)}
              error={errors.maintenanceAlerts as string}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <SettingsActionButtons
        onSave={onSave}
        onReset={onReset}
        loading={loading}
        hasChanges={hasChanges}
      />
    </div>
  );
};

export default AlertNotificationSection;
