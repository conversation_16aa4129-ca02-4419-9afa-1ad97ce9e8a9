"use client";
import React from "react";
import { AlertNotificationSettings } from "@/types/systemSettingsTypes";
import SettingsToggle from "../SettingsToggle";

interface AlertNotificationSectionProps {
  data: AlertNotificationSettings;
  errors: Partial<AlertNotificationSettings>;
  onChange: (field: keyof AlertNotificationSettings, value: boolean) => void;
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

const AlertNotificationSection: React.FC<AlertNotificationSectionProps> = ({
  data,
  errors,
  onChange,
}) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-[#3D3D3D] pb-4">
        <h2 className="text-xl text-white font-medium">
          Alert & Notification Settings
        </h2>
        <p className="text-[#A3A3A3] text-sm mt-1">
          Customize how your system communicates security events, alerts, and
          escalations.
        </p>
      </div>

      {/* General Notifications Section */}
      <div className="border-b border-[#3D3D3D] pb-8">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg text-white font-medium">
              General Notifications
            </h3>
          </div>

          <div className="flex-1 space-y-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="text-white font-medium mb-2">Activity Alerts</h4>
                <p className="text-[#A3A3A3] text-xs line-clamp-2">
                  Get notified for door access logs, face recognition events, or
                  device status changes.
                </p>
              </div>
              <div className="ml-6">
                <SettingsToggle
                  id="activityAlerts"
                  label=""
                  description=""
                  checked={data.emailNotifications}
                  onChange={(checked) =>
                    onChange("emailNotifications", checked)
                  }
                  error={
                    errors.emailNotifications
                      ? String(errors.emailNotifications)
                      : ""
                  }
                />
              </div>
            </div>

            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="text-white font-medium mb-2">
                  System Health Updates
                </h4>
                <p className="text-[#A3A3A3] text-xs line-clamp-2">
                  Stay updated when cameras go offline or security devices fail.
                </p>
              </div>
              <div className="ml-6">
                <SettingsToggle
                  id="systemHealthUpdates"
                  label=""
                  description=""
                  checked={data.systemUpdates}
                  onChange={(checked) => onChange("systemUpdates", checked)}
                  error={
                    errors.systemUpdates ? String(errors.systemUpdates) : ""
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Critical Security Alerts Section */}
      <div className="border-b border-[#3D3D3D] pb-8">
        <div className="flex items-center justify-between">
          <h3 className="text-lg text-white font-medium">
            Critical Security Alerts
          </h3>
          <div
            className="text-white py-4 px-10 w-fit"
            style={{
              background:
                "linear-gradient(145.42deg, rgba(31, 31, 31, 0.5) 32.16%, rgba(112, 112, 112, 0.5) 237.44%)",
            }}
          >
            <span>Always Active</span>
          </div>
        </div>
      </div>

      {/* Emergency Alert Setup Section */}
      <div>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg text-white font-medium">
              Emergency Alert Setup
            </h3>
          </div>

          <div className="flex-1 space-y-6">
            <div className="flex items-center justify-between">
              <span className="text-white font-medium">
                Primary Emergency Contact
              </span>
              <button
                type="button"
                className="px-4 py-2 border border-[#A3A3A3] text-[#A3A3A3] text-sm"
              >
                + Add
              </button>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-white font-medium">
                Backup Emergency Line
              </span>
              <button
                type="button"
                className="px-4 py-2 border border-[#A3A3A3] text-[#A3A3A3] text-sm"
              >
                + Add
              </button>
            </div>

            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="text-white font-medium mb-2">
                  Panic Alert Button
                </h4>
                <p className="text-[#A3A3A3] text-xs line-clamp-2">
                  Trigger emergency alert and notify responders
                </p>
              </div>
              <div className="ml-6">
                <SettingsToggle
                  id="panicAlertButton"
                  label=""
                  description=""
                  checked={data.securityAlerts}
                  onChange={(checked) => onChange("securityAlerts", checked)}
                  error={
                    errors.securityAlerts ? String(errors.securityAlerts) : ""
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertNotificationSection;
