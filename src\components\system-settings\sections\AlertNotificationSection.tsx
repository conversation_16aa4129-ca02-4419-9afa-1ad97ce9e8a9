"use client";
import React from "react";
import { AlertNotificationSettings } from "@/types/systemSettingsTypes";
import SettingsToggle from "../SettingsToggle";

interface AlertNotificationSectionProps {
  data: AlertNotificationSettings;
  errors: Partial<AlertNotificationSettings>;
  onChange: (field: keyof AlertNotificationSettings, value: boolean) => void;
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

const AlertNotificationSection: React.FC<AlertNotificationSectionProps> = ({
  data,
  errors,
  onChange,
}) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-[#3D3D3D] pb-4">
        <h2 className="text-xl text-white font-medium">Alert & Notification Settings</h2>
        <p className="text-[#A3A3A3] text-sm mt-1">
          Customize how your system communicates security events, alerts, and escalations.
        </p>
      </div>

      {/* General Notifications */}
      <div>
        <h3 className="text-lg text-white font-medium mb-6">General Notifications</h3>
        <div className="space-y-6">
          <div className="flex items-center justify-between py-4 border-b border-[#3D3D3D]">
            <div>
              <h4 className="text-white font-medium">Activity Alerts</h4>
              <p className="text-[#A3A3A3] text-sm mt-1">
                Get notified for door access logs, face recognition events, or device status changes.
              </p>
            </div>
            <SettingsToggle
              id="activityAlerts"
              label=""
              description=""
              checked={data.emailNotifications}
              onChange={(checked) => onChange("emailNotifications", checked)}
              error={errors.emailNotifications as string}
            />
          </div>

          <div className="flex items-center justify-between py-4 border-b border-[#3D3D3D]">
            <div>
              <h4 className="text-white font-medium">System Health Updates</h4>
              <p className="text-[#A3A3A3] text-sm mt-1">
                Stay updated when cameras go offline or security devices fail.
              </p>
            </div>
            <SettingsToggle
              id="systemHealthUpdates"
              label=""
              description=""
              checked={data.systemUpdates}
              onChange={(checked) => onChange("systemUpdates", checked)}
              error={errors.systemUpdates as string}
            />
          </div>
        </div>
      </div>

      {/* Critical Security Alerts */}
      <div>
        <div className="flex items-center justify-between py-4 border-b border-[#3D3D3D]">
          <h3 className="text-lg text-white font-medium">Critical Security Alerts</h3>
          <span className="text-[#A3A3A3] text-sm">Always Active</span>
        </div>
      </div>

      {/* Emergency Alert Setup */}
      <div>
        <h3 className="text-lg text-white font-medium mb-6">Emergency Alert Setup</h3>
        <div className="space-y-6">
          <div className="flex items-center justify-between py-4 border-b border-[#3D3D3D]">
            <span className="text-white font-medium">Primary Emergency Contact</span>
            <button className="px-4 py-2 border border-[#3D3D3D] text-[#A3A3A3] text-sm rounded hover:bg-[#2E2E2E] transition-colors">
              + Add
            </button>
          </div>

          <div className="flex items-center justify-between py-4 border-b border-[#3D3D3D]">
            <span className="text-white font-medium">Backup Emergency Line</span>
            <button className="px-4 py-2 border border-[#3D3D3D] text-[#A3A3A3] text-sm rounded hover:bg-[#2E2E2E] transition-colors">
              + Add
            </button>
          </div>

          <div className="flex items-center justify-between py-4">
            <div>
              <h4 className="text-white font-medium">Panic Alert Button</h4>
              <p className="text-[#A3A3A3] text-sm mt-1">
                Trigger emergency alert and notify responders
              </p>
            </div>
            <SettingsToggle
              id="panicAlertButton"
              label=""
              description=""
              checked={data.securityAlerts}
              onChange={(checked) => onChange("securityAlerts", checked)}
              error={errors.securityAlerts as string}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertNotificationSection;
