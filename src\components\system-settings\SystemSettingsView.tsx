"use client";
import React, { useState } from "react";
import SettingsSidebar from "./SettingsSidebar";
import GeneralPreferencesSection from "./sections/GeneralPreferencesSection";
import AlertNotificationSection from "./sections/AlertNotificationSection";

import { SettingsCategory } from "@/types/systemSettingsTypes";
import { useSystemSettings } from "./hooks";

const SystemSettingsView: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<SettingsCategory>("general");
  const {
    formData,
    errors,
    loading,
    hasChanges,
    handleGeneralPreferencesChange,
    handleAlertNotificationChange,
    handleSave,
    handleReset,
  } = useSystemSettings();

  const renderActiveSection = () => {
    switch (activeCategory) {
      case "general":
        return (
          <GeneralPreferencesSection
            data={formData.generalPreferences}
            errors={errors.generalPreferences}
            onChange={handleGeneralPreferencesChange}
          />
        );
      case "notifications":
        return (
          <AlertNotificationSection
            data={formData.alertNotificationSettings}
            errors={errors.alertNotificationSettings}
            onChange={handleAlertNotificationChange}
            onSave={handleSave}
            onReset={handleReset}
            loading={loading}
            hasChanges={hasChanges}
          />
        );
      case "faceDirectory":
        return (
          <div className="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-4">
            <div className="text-xl text-white">Coming Soon</div>
            <p className="text-[#A3A3A3] text-sm max-w-md">
              Face Directory Management features are currently under development and will be available in a future update.
            </p>
          </div>
        );
      case "security":
        return (
          <div className="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-4">
            <div className="text-xl text-white">Coming Soon</div>
            <p className="text-[#A3A3A3] text-sm max-w-md">
              Security Preferences features are currently under development and will be available in a future update.
            </p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex gap-8 h-full">
      {/* Settings Sidebar */}
      <div className="w-68 flex-shrink-0">
        <SettingsSidebar
          activeCategory={activeCategory}
          onCategoryChange={setActiveCategory}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0">
        {renderActiveSection()}
      </div>
    </div>
  );
};

export default SystemSettingsView;
