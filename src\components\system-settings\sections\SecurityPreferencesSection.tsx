"use client";
import React from "react";
import { SecurityPreferences } from "@/types/systemSettingsTypes";
import SettingsToggle from "../SettingsToggle";
import SettingsSlider from "../SettingsSlider";
import SettingsActionButtons from "../SettingsActionButtons";

interface SecurityPreferencesSectionProps {
  data: SecurityPreferences;
  errors: Partial<SecurityPreferences>;
  onChange: (field: keyof SecurityPreferences | string, value: any) => void;
  onSave: () => void;
  onReset: () => void;
  loading: boolean;
  hasChanges: boolean;
}

const SecurityPreferencesSection: React.FC<SecurityPreferencesSectionProps> = ({
  data,
  errors,
  onChange,
  onSave,
  onReset,
  loading,
  hasChanges,
}) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-[#3D3D3D] pb-4">
        <h2 className="text-xl text-white font-medium">Security Preferences</h2>
        <p className="text-[#A3A3A3] text-sm mt-1">
          Configure security policies and authentication settings.
        </p>
      </div>

      {/* Settings Form */}
      <div className="space-y-6">
        {/* Session Management */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Session Management</h3>
          <div className="space-y-4">
            <SettingsSlider
              id="sessionTimeout"
              label="Session Timeout"
              description="Automatically log out users after this period of inactivity"
              value={data.sessionTimeout}
              min={5}
              max={120}
              step={5}
              unit=" minutes"
              onChange={(value) => onChange("sessionTimeout", value)}
              error={errors.sessionTimeout as string}
            />
          </div>
        </div>

        {/* Password Policy */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Password Policy</h3>
          <div className="space-y-4">
            <SettingsSlider
              id="passwordMinLength"
              label="Minimum Password Length"
              description="Minimum number of characters required for passwords"
              value={data.passwordPolicy.minLength}
              min={6}
              max={20}
              step={1}
              unit=" characters"
              onChange={(value) => onChange("passwordPolicy.minLength", value)}
              error={errors.passwordPolicy?.minLength as string}
            />
            
            <SettingsToggle
              id="requireUppercase"
              label="Require Uppercase Letters"
              description="Passwords must contain at least one uppercase letter"
              checked={data.passwordPolicy.requireUppercase}
              onChange={(checked) => onChange("passwordPolicy.requireUppercase", checked)}
              error={errors.passwordPolicy?.requireUppercase as string}
            />
            
            <SettingsToggle
              id="requireLowercase"
              label="Require Lowercase Letters"
              description="Passwords must contain at least one lowercase letter"
              checked={data.passwordPolicy.requireLowercase}
              onChange={(checked) => onChange("passwordPolicy.requireLowercase", checked)}
              error={errors.passwordPolicy?.requireLowercase as string}
            />
            
            <SettingsToggle
              id="requireNumbers"
              label="Require Numbers"
              description="Passwords must contain at least one number"
              checked={data.passwordPolicy.requireNumbers}
              onChange={(checked) => onChange("passwordPolicy.requireNumbers", checked)}
              error={errors.passwordPolicy?.requireNumbers as string}
            />
            
            <SettingsToggle
              id="requireSpecialChars"
              label="Require Special Characters"
              description="Passwords must contain at least one special character"
              checked={data.passwordPolicy.requireSpecialChars}
              onChange={(checked) => onChange("passwordPolicy.requireSpecialChars", checked)}
              error={errors.passwordPolicy?.requireSpecialChars as string}
            />
          </div>
        </div>

        {/* Authentication */}
        <div>
          <h3 className="text-lg text-white font-medium mb-4">Authentication</h3>
          <div className="space-y-4">
            <SettingsToggle
              id="twoFactorAuth"
              label="Two-Factor Authentication"
              description="Require additional verification for login"
              checked={data.twoFactorAuth}
              onChange={(checked) => onChange("twoFactorAuth", checked)}
              error={errors.twoFactorAuth as string}
            />
            
            <SettingsSlider
              id="loginAttempts"
              label="Maximum Login Attempts"
              description="Number of failed login attempts before account lockout"
              value={data.loginAttempts}
              min={3}
              max={10}
              step={1}
              unit=" attempts"
              onChange={(value) => onChange("loginAttempts", value)}
              error={errors.loginAttempts as string}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <SettingsActionButtons
        onSave={onSave}
        onReset={onReset}
        loading={loading}
        hasChanges={hasChanges}
      />
    </div>
  );
};

export default SecurityPreferencesSection;
