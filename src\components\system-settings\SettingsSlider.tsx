"use client";
import React from "react";

interface SettingsSliderProps {
  id: string;
  label: string;
  description?: string;
  value: number;
  min: number;
  max: number;
  step: number;
  unit?: string;
  onChange: (value: number) => void;
  error?: string;
  disabled?: boolean;
}

const SettingsSlider: React.FC<SettingsSliderProps> = ({
  id,
  label,
  description,
  value,
  min,
  max,
  step,
  unit = "",
  onChange,
  error,
  disabled = false,
}) => {
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <label htmlFor={id} className="block text-lg text-white font-medium mb-2">
            {label}
          </label>
          {description && (
            <p className="text-sm text-[#A3A3A3] max-w-xs">{description}</p>
          )}
        </div>
        <div className="flex-shrink-0 ml-4">
          <span className={`text-sm font-medium ${disabled ? "text-[#6A6A6A]" : "text-white"}`}>
            {value}{unit}
          </span>
        </div>
      </div>
      
      <div className="relative">
        <input
          type="range"
          id={id}
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          disabled={disabled}
          className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${
            disabled ? "opacity-50 cursor-not-allowed" : ""
          }`}
          style={{
            background: disabled
              ? "#3D3D3D"
              : `linear-gradient(to right, #A3A3A3 0%, #A3A3A3 ${percentage}%, #3D3D3D ${percentage}%, #3D3D3D 100%)`,
          }}
        />
        <style jsx>{`
          input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: ${disabled ? "#6A6A6A" : "#FFFFFF"};
            cursor: ${disabled ? "not-allowed" : "pointer"};
            border: 2px solid ${disabled ? "#3D3D3D" : "#A3A3A3"};
          }
          
          input[type="range"]::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: ${disabled ? "#6A6A6A" : "#FFFFFF"};
            cursor: ${disabled ? "not-allowed" : "pointer"};
            border: 2px solid ${disabled ? "#3D3D3D" : "#A3A3A3"};
            box-sizing: border-box;
          }
        `}</style>
      </div>
      
      <div className="flex justify-between text-xs text-[#A3A3A3]">
        <span>{min}{unit}</span>
        <span>{max}{unit}</span>
      </div>
      
      {error && (
        <p className="text-red-500 text-xs mt-1 error-message">{error}</p>
      )}
    </div>
  );
};

export default SettingsSlider;
